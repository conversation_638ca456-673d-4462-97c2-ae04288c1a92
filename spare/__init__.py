"""
SpARE: Sparse Auto-Encoder based Representation Engineering

A comprehensive Python library for training and using Sparse Auto-Encoders (SAEs)
with any HuggingFace transformer model for representation engineering tasks.

Key Features:
- Universal HuggingFace model support
- Flexible dataset handling
- Multiple SAE architectures (standard, gated, jumprelu)
- Comprehensive evaluation tools
- Easy-to-use API

Example:
    >>> import spare
    >>> config = spare.SAEConfig(model_name="microsoft/DialoGPT-medium")
    >>> trainer = spare.SAETrainer(config)
    >>> sae = trainer.train()
"""

__version__ = "1.0.0"
__author__ = "Yu Zhao"
__email__ = "<EMAIL>"

# Core SAE components
from .core import (
    SAEConfig,
    SAETrainer,
    SAE,
    UniversalModelLoader,
    DatasetManager,
)

# Analysis and evaluation tools
from .analysis import (
    ActivationAnalyzer,
    FunctionExtractor,
    RepresentationEngineer,
)

# Utilities
from .utils import (
    load_model_and_tokenizer,
    setup_logging,
    validate_config,
)

__all__ = [
    # Core components
    "SAEConfig",
    "SAETrainer",
    "SAE",
    "UniversalModelLoader",
    "DatasetManager",
    # Analysis tools
    "ActivationAnalyzer",
    "FunctionExtractor",
    "RepresentationEngineer",
    # Utilities
    "load_model_and_tokenizer",
    "setup_logging",
    "validate_config",
]
