from spare.sae import Sae

def load_sae(model_name, layer_idx):
    if model_name == "Meta-Llama-3-1-8B":
        sae = Sae.load_from_hub(
            "EleutherAI/sae-llama-3.1-8b-32x", hookpoint=f"layers.{layer_idx}.mlp"
        )
    else:
        raise NotImplementedError(f"sae for {model_name}")

    for pn, p in sae.named_parameters():
        p.requires_grad = False
    sae.cuda()
    return sae

load_sae("Meta-Llama-3-1-8B", 12)

sae = Sae.load_from_hub("EleutherAI/sae-llama-3.1-8b-32x", hookpoint="layers.23.mlp")

